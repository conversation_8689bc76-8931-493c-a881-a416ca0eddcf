<template>
  <div class="table-main report-page">
    <div class="flex flex-wrap header card button-group">
      <el-form :inline="true" class="report-query-form" style="width: 100%">
        <el-form-item>
          <el-checkbox v-model="isDateCheck">{{ t("report.date") }}：</el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-date-picker v-model="dateRange" type="datetimerange"></el-date-picker>
        </el-form-item>
      </el-form>
      <div class="header-button-ri" style="width: 100%; margin-top: 4px; text-align: right">
        <el-button type="primary" :icon="Search" :disabled="isButtonClick" @click="searchReport">{{ t("report.search") }}</el-button>
        <el-button type="success" :icon="Folder" :disabled="isButtonClick" @click="exportReport">{{ t("report.save") }}</el-button>
        <el-button type="danger" plain :icon="Delete" @click="clearList">{{ t("report.clearList") }}</el-button>
      </div>
    </div>
    <div class="card table-box report-table">
      <el-table
        v-loading="tableLoad"
        :data="tableData"
        :cell-class-name="'no-warp-cell'"
        border
        :max-height="getTableMaxHeight(220)"
        :stripe="true"
        :element-loading-text="t('report.loading')"
      >
        <el-table-column :label="t('report.entryID')" prop="entryID" align="center"></el-table-column>
        <el-table-column :label="t('report.module')" prop="module" align="center"></el-table-column>
        <el-table-column :label="t('report.msg')" prop="msg" align="center"></el-table-column>
        <el-table-column :label="t('report.level')" prop="level" align="center"></el-table-column>
        <el-table-column :label="t('report.type')" prop="type" align="center"></el-table-column>
        <el-table-column :label="t('report.origin')" prop="orig" align="center"></el-table-column>
        <el-table-column :label="t('report.time')" prop="time" align="center"></el-table-column>
        <el-table-column :label="t('report.result')" prop="result" align="center"></el-table-column>
        <el-table-column :label="t('report.user')" prop="user" align="center"></el-table-column>
      </el-table>
    </div>
  </div>
  <el-dialog
    v-model="showDialog"
    width="60%"
    class="hover"
    :align-center="true"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :show-close="false"
    draggable
    :title="t('report.progress')"
  >
    <div style="height: 120px; padding: 15px">
      <el-progress :percentage="dialogShow.percentage" :text-inside="false" striped striped-flow style="margin-top: 60px">
        <span>{{ dialogShow.progressText }}</span>
      </el-progress>
    </div>
  </el-dialog>
  <ProgressDialog ref="progressDialog" />
</template>
<script setup lang="ts">
import { Delete, Search, Folder } from "@element-plus/icons-vue";
import Message from "@/scripts/message";
import { ipc } from "@/api/request/ipcRenderer";
import { getDateZh, getTableMaxHeight } from "@/utils/index";
import { IECNotify, reportApi, ReportParam, ResultData } from "@/api";
import { useDebugStore } from "@/stores/modules/debug";
import { genRpcTimeParam } from "@/utils/iec/iecRpcUtils";
import { osControlApi } from "@/api/modules/biz/os";
import { ref, reactive } from "vue";
import { useI18n } from "vue-i18n";
import { ElMessageBox } from "element-plus";
import ProgressDialog from "../dialog/ProgressDialog.vue";

const { t } = useI18n();
// 透传装置ID
const props = defineProps<{ deviceId: string }>();
const { report, currDevice, addConsole } = useDebugStore();
const globalReport = report.get(currDevice.id);
const yesterday = new Date();
yesterday.setDate(yesterday.getDate() - 1);
const dateRange = ref<[Date, Date]>([yesterday, new Date()]);
const isButtonClick = ref<boolean>(false);
const tableLoad = ref<boolean>(false);
const tableData = ref<ReportParam.IECRpcAuditLogReportRes[]>([]);
const dialogShow = reactive({
  percentage: 0,
  progressText: ""
});
const showDialog = ref(false);
const progressDialog = ref();
const getTableData = (): ReportParam.IECRpcAuditLogReportRes[] => {
  return globalReport!.auditlogReport.get(globalReport?.newname || globalReport?.currReportType || "") || [];
};
const isDateCheck = ref<boolean>(false);
const clearList = (): void => {
  globalReport!.auditlogReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  tableData.value = [];
};

const searchReport = async (): Promise<void> => {
  if (isDateCheck.value && dateRange.value.length < 2) {
    Message.warning(t("report.selectCompleteTimeRange"));
    return;
  }
  const arg: ReportParam.IECRpcReportSearchParam = {
    type: globalReport!.currReportType,
    startTime: isDateCheck.value ? genRpcTimeParam(dateRange.value[0]) : "",
    stopTime: isDateCheck.value ? genRpcTimeParam(dateRange.value[1]) : ""
  };
  globalReport!.auditlogReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
  tableLoad.value = true;
  isButtonClick.value = true;
  dialogShow.percentage = 0;
  showDialog.value = true; // 弹窗立即显示
  const reportDesc: string = globalReport!.currReportDesc;
  dialogShow.progressText = t("report.querying") + reportDesc;
  startFakeProgress();
  // 记录开始时间
  const start = Date.now();
  const res: ResultData<any> = await reportApi.getAuditReportListByDevice(props.deviceId, arg);
  // 计算已用时，若不足500ms则补足
  const elapsed = Date.now() - start;
  if (elapsed < 500) {
    await new Promise(resolve => setTimeout(resolve, 500 - elapsed));
  }
  stopFakeProgress();
  showDialog.value = false;
  if (res.code != 1) {
    Message.warning(res.msg);
    return;
  }
};

let fakeProgressTimer: any = null;
function startFakeProgress() {
  dialogShow.percentage = 0;
  console.log("startFakeProgress: percentage =", dialogShow.percentage);
  if (fakeProgressTimer) clearInterval(fakeProgressTimer);
  fakeProgressTimer = setInterval(() => {
    if (dialogShow.percentage < 95) {
      dialogShow.percentage += 5;
      console.log("progress", dialogShow.percentage);
    }
  }, 100);
}
function stopFakeProgress() {
  if (fakeProgressTimer) {
    clearInterval(fakeProgressTimer);
    fakeProgressTimer = null;
  }
  dialogShow.percentage = 100;
}

const exportReport = async (): Promise<void> => {
  if (getTableData().length == 0) {
    Message.warning(t("report.noDataToSave"));
    addConsole(t("report.exportLogFailed", { msg: t("report.noDataToSave") }));
    return;
  }
  // 点击保存后立即显示进度条，提示选择保存路径，但不启动假进度
  // progressDialog.value.setProgress(0, t("report.pleaseSelectSavePath"), true);
  progressDialog.value.show();
  // 先选择保存路径
  const reportDesc: string = globalReport!.currReportDesc;
  const path = await osControlApi.openSaveFileDialogByParams({
    title: t("report.saveReport"),
    defaultPath: reportDesc + getDateZh(),
    filterList: [
      { name: "Rpt", extensions: ["rpt"] },
      { name: "Excel", extensions: ["xlsx"] }
    ]
  });
  if (!path) {
    progressDialog.value.hide();
    addConsole(t("report.exportLogFailed", { msg: t("report.saveFailed") }));
    return;
  }
  // 选择后再切换进度条文本，并启动假进度
  // progressDialog.value.show();
  const exportList: any[] = [];
  getTableData().forEach(obj => {
    exportList.push({
      entryID: obj.entryID,
      module: obj.module,
      msg: obj.msg,
      type: obj.type,
      level: obj.level,
      orig: obj.orig,
      time: obj.time,
      result: obj.result,
      user: obj.user
    });
  });
  const arg: ReportParam.IECRpcCommonReportExportParam = {
    type: globalReport!.currReportType,
    method: globalReport!.currReportMethod,
    path: path as unknown as string,
    items: exportList
  };
  const res: ResultData<any> = await reportApi.exportCommonReportByDevice(props.deviceId, arg);
  progressDialog.value.hide();
  if (res.code != 1) {
    Message.warning(res.msg);
    addConsole(t("report.exportLogFailed", { msg: res.msg }));
    return;
  }
  Message.success(t("report.saveSuccess"));
  addConsole(t("report.exportLogSuccess", { path }));
  await ElMessageBox.alert(t("report.saveSuccess"), t("report.progress"), {
    confirmButtonText: t("common.confirm") || "确定",
    type: "success"
  });
};

const initTableData = (): void => {
  if (getTableData().length > 0) {
    dialogShow.percentage = 50;
    showDialog.value = true;
    dialogShow.progressText = t("report.loadingText");
  }
  nextTick(() => {
    setTimeout(() => {
      tableData.value = getTableData();
      dialogShow.percentage = 100;
      showDialog.value = false;
    }, 50);
  });
};

ipc.on("report_notify", (_event: unknown, notify: IECNotify) => {
  // 多装置过滤：仅处理当前组件对应的 deviceId 事件
  if (notify.deviceId && notify.deviceId !== props.deviceId) return;
  const reportData = notify.data as any;
  if (notify.type == "readAuditLogReport") {
    globalReport!.auditlogReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
    if (reportData.code != 1) {
      tableLoad.value = false;
      isButtonClick.value = false;
      dialogShow.percentage = 100;
      showDialog.value = false;
      Message.warning(reportData.msg);
      return;
    }
    globalReport!.auditlogReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
    if (Array.isArray(reportData.data)) {
      let filteredList = reportData.data;
      if (globalReport!.keyword && globalReport!.keyword.trim() !== "") {
        filteredList = reportData.data.filter(item => (item.name || "").toLowerCase().includes(globalReport!.keyword.toLowerCase()));
      }
      tableData.value = filteredList;
      globalReport!.auditlogReport.set(globalReport?.newname || globalReport?.currReportType || "", filteredList);
    } else {
      globalReport!.auditlogReport.set(globalReport?.newname || globalReport?.currReportType || "", []);
      tableData.value = [];
    }
    tableLoad.value = false;
    isButtonClick.value = false;
    dialogShow.percentage = 100;
    showDialog.value = false;
  }
});

onMounted(() => {
  initTableData();
});
watch(
  () => globalReport!.currReportDesc,
  () => {
    initTableData();
  }
);
onBeforeUnmount(() => {
  ipc.removeAllListeners("report_notify");
});
</script>
<style scoped lang="scss">
.report-page {
  margin-top: 5px;
  .button-group {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    .search-page {
      font-size: 14px;
    }
    .his-var {
      font-size: 14px;
    }
    .el-row {
      div {
        font-size: 14px;
        :deep(.el-date-editor) {
          width: 340px;
        }
      }
    }
  }
  .report-table {
    overflow-y: auto;
    scrollbar-width: none;
  }
}
.header {
  margin-bottom: 5px;
}
.no-warp-cell .cell {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}
</style>
