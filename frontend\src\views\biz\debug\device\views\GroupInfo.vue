<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :pagination="false"
      :init-param="initParam"
      :request-auto="false"
      :data="tableData"
      highlight-current-row
      @search="getGroupInfo"
      row-key="name"
      table-key="groupInfo"
    >
      <template #expand="scope">
        {{ scope.row }}
      </template>
    </ProTable>
  </div>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="tsx" name="useProTable">
import { ref, reactive } from "vue";
import { useI18n } from "vue-i18n";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { GroupInfoItem } from "@/api/interface/biz/debug/debuginfo";
import { deviceInfoMenutreeApi } from "@/api";
// import { variableApi } from "@/api/modules/biz/debug/variable";
import { useDebugStore } from "@/stores/modules/debug";
const { debugIndex, currDevice } = useDebugStore();
import ProgressDialog from "../dialog/ProgressDialog.vue";
const progressDialog = ref();
const { t } = useI18n();
// ProTable 实例
const proTable = ref<ProTableInstance>();
// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 });

const tableData = ref<GroupInfoItem[]>([]);
// 差异数据
// onMounted(() => {
//   proTable.value?.refresh();
// });

// 如果你想在请求之前对当前请求参数做一些操作，可以自定义如下函数：params 为当前所有的请求参数（包括分页），最后返回请求列表接口
// 默认不做操作就直接在 ProTable 组件上绑定	:requestApi="getUserList"
const getGroupInfo = async () => {
  let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
  newParams.createTime && (newParams.startTime = newParams.createTime[0]);
  newParams.createTime && (newParams.endTime = newParams.createTime[1]);
  newParams.grpName = debugIndex.compData.get(currDevice.id).name;
  newParams.label = debugIndex.compData.get(currDevice.id).label;
  delete newParams.createTime;
  // showLoading();
  try {
    const result = await deviceInfoMenutreeApi.getGroupInfoListByDevice(currDevice.id, newParams);
    console.log(t("device.groupInfo.messages.fetchedData"), result);
    tableData.value = Array.isArray(result.data) ? result.data : [];
    proTable.value?.refresh();
    // return result || { list: [], total: 0 };
  } catch (error) {
    console.error(t("device.groupInfo.messages.fetchDataError"), error);
    // return { list: [], total: 0 };
  }
};

onMounted(() => {
  // getData({});
  getGroupInfo();
});

// 表格配置项
const columns = reactive<ColumnProps<GroupInfoItem>[]>([
  {
    prop: "id",
    label: t("device.groupInfo.table.id"),
    width: 80
  },
  {
    prop: "name",
    label: t("device.groupInfo.table.name"),
    width: 200
  },
  {
    prop: "desc",
    label: t("device.groupInfo.table.desc"),
    width: 300
  },
  {
    prop: "fc",
    label: t("device.groupInfo.table.fc"),
    width: 200
  },
  {
    prop: "count",
    label: t("device.groupInfo.table.count")
  }
]);

watch(
  debugIndex.compData,
  newValue => {
    console.log("newValue:", newValue);
    if (newValue) {
      console.log("grpName", newValue);
      proTable.value?.reset();
      getGroupInfo();
    }
  },
  { deep: true }
);
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 8px;
}
</style>
