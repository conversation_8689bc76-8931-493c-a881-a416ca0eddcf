<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :init-param="initParam"
      :request-auto="false"
      :data="tableData"
      :data-callback="dataCallback"
      row-key="name"
      @search="getData"
      :pagination="false"
      highlight-current-row
      :tool-button="false"
      table-key="remoteYm"
    >
      <template #operation="scope">
        <el-button type="primary" link :icon="SemiSelect" @click="semiSelect(scope.row)">{{ t("device.remoteYm.directControl") }}</el-button>
        <el-button type="primary" link :icon="SuitcaseLine" @click="suitcaseLine(scope.row)">{{ t("device.remoteYm.selectControl") }}</el-button>
      </template>
      <!-- Expand -->
      <template #expand="scope">
        {{ scope.row }}
      </template>
    </ProTable>
  </div>
  <ProgressDialog ref="progressDialog"></ProgressDialog>
</template>

<script setup lang="tsx" name="useProTa1ble">
import { ref, reactive } from "vue";
import { useI18n } from "vue-i18n";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDebugStore } from "@/stores/modules/debug";
import { DebugInfoItem } from "@/api/interface/biz/debug/debuginfo";
import { deviceInfoMenutreeApi } from "@/api/modules/biz/debug";
import { ResultData } from "@/api/interface";
import { SemiSelect, SuitcaseLine } from "@element-plus/icons-vue";
import { CtlCheck, CtlMode, MenuIdName, SelectRequestData } from "@/api/interface/biz/debug/remote";
import { remoteControlApi } from "@/api/modules/biz/debug/remoteoperate";
import ProgressDialog from "../dialog/ProgressDialog.vue";
const { debugIndex, currDevice } = useDebugStore();
const tableData = ref<DebugInfoItem[]>([]);
const progressDialog = ref();
const { addConsole } = useDebugStore();
const proTable = ref<ProTableInstance>();
const { t } = useI18n();

// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 });

const semiSelect = async (row: any) => {
  console.log(row);
  const { isValid } = isInputValid(row.value);
  if (!isValid) {
    addConsole(t("device.remoteYm.invalidData", { name: row.name, value: row.value || "" }));
    ElMessageBox.alert(t("device.remoteYm.invalidData", { name: row.name, value: row.value || "" }), t("device.remoteYm.error"), { type: "error" });
    return;
  }

  showLoading();

  try {
    const param: SelectRequestData = {
      /** 控制源即name */
      ctlObj: row.name,
      /** 值 */
      ctlVal: String(row.value),
      /** 模式 sbo=选控，direct=直控 */
      ctlModel: CtlMode.DIRECT,
      /** 当前时间iso8601格式 */
      opemTm: new Date().toISOString(),
      test: "0",
      check: CtlCheck.NO_CHECK
    };

    remoteConfirm(param, "execute");
  } finally {
    hideLoading();
  }
};

const remoteConfirm = async (param: SelectRequestData, command: string) => {
  if (command == "execute") {
    const res = await remoteControlApi.ykSelectValueConfirmByDevice(props.deviceId, param);
    if (res.code == 0) {
      ElMessageBox.alert(t("device.remoteYm.executeSuccess"), t("device.remoteYm.prompt"), {
        confirmButtonText: t("device.remoteYm.confirmButton"),
        type: "success"
      });
    } else {
      ElMessageBox.alert(res.msg, t("device.remoteYm.error"), {
        confirmButtonText: t("device.remoteYm.confirmButton"),
        type: "error"
      });
    }
  } else {
    const res = await remoteControlApi.ykSelectValueCancelByDevice(props.deviceId, param);
    if (res.code == 0) {
      ElMessageBox.alert(t("device.remoteYm.cancelSuccess"), t("device.remoteYm.prompt"), {
        confirmButtonText: t("device.remoteYm.confirmButton"),
        type: "success"
      });
    } else {
      ElMessageBox.alert(res.msg, t("device.remoteYm.error"), {
        confirmButtonText: t("device.remoteYm.confirmButton"),
        type: "error"
      });
    }
  }
};
const suitcaseLine = async (row: any) => {
  console.log(row);
  const { isValid } = isInputValid(row.value);
  if (!isValid) {
    addConsole(t("device.remoteYm.invalidData", { name: row.name, value: row.value || "" }));
    ElMessageBox.alert(t("device.remoteYm.invalidData", { name: row.name, value: row.value || "" }), t("device.remoteYm.error"), { type: "error" });
    return;
  }

  try {
    showLoading();
    const param: SelectRequestData = {
      /** 控制源即name */
      ctlObj: row.name,
      /** 值 */
      ctlVal: String(row.value),
      /** 模式 sbo=选控，direct=直控 */
      ctlModel: CtlMode.SBO,
      /** 当前时间iso8601格式 */
      opemTm: new Date().toISOString(),
      test: "0",
      check: CtlCheck.NO_CHECK
    };
    const res = await remoteControlApi.ykSelectWithValueByDevice(props.deviceId, param);
    if (res.code == 0) {
      ElMessageBox.confirm(t("device.remoteYm.confirmExecute"), t("device.remoteYm.confirm"), {
        confirmButtonText: t("device.remoteYm.executeButton"),
        cancelButtonText: t("device.remoteYm.cancelButton"),
        closeOnClickModal: false,
        type: "info"
      })
        .then(() => {
          remoteConfirm(param, "execute");
        })
        .catch(() => {
          remoteConfirm(param, "abort");
        });
    } else if (res.code == 2) {
      if (res.data) {
        ElMessageBox.alert(res.msg, t("device.remoteYm.error"), {
          confirmButtonText: t("device.remoteYm.confirmButton"),
          type: "error"
        });
      } else {
        ElMessageBox.alert(res.msg, t("device.remoteYm.error"), {
          confirmButtonText: t("device.remoteYm.confirmButton"),
          type: "error"
        });
      }
    } else {
      ElMessageBox.alert(res.msg, t("device.remoteYm.error"), {
        confirmButtonText: t("device.remoteYm.confirmButton"),
        type: "error"
      });
    }
  } finally {
    hideLoading();
  }
};

const dataCallback = (data: any) => {
  console.log("dataCallback:", data);
  return {
    list: data.list,
    total: data.total
  };
};

const showLoading = () => {
  progressDialog.value.show();
};
const hideLoading = () => {
  progressDialog.value.hide();
};
const getData = async () => {
  //showLoading();
  try {
    console.log("getData");
    let newParams = JSON.parse(JSON.stringify(proTable.value?.searchParam));
    console.log(newParams);
    const param: MenuIdName = {
      id: currDevice.id,
      type: "submenu",
      names: [debugIndex.compData.get(currDevice.id).pname, debugIndex.compData.get(currDevice.id).name]
    };
    const res: ResultData<DebugInfoItem[]> = await deviceInfoMenutreeApi.getDeviceMenuItem(param);
    if (!res.data) {
      throw new Error("Invalid response structure");
    }
    let resultData = res.data;
    if (newParams.name) {
      resultData = res.data?.filter(data => {
        return data.name.toLowerCase().includes(newParams.name.toLowerCase());
      });
      console.log(resultData);
    }
    if (newParams.desc) {
      resultData = res.data?.filter(data => {
        return data.desc.toLowerCase().includes(newParams.desc.toLowerCase());
      });
      console.log(resultData);
    }
    tableData.value = Array.isArray(resultData) ? resultData : [];
    proTable.value?.refresh();
  } finally {
    // hideLoading();
  }
};

const isInputValid = input => {
  // 转换为字符串并去除首尾空格
  const str = String(input).trim();

  // 空字符串直接返回无效
  if (str === "") return { isValid: false, type: null };

  // 检查是否为整数
  const isInteger = /^[-+]?\d+$/.test(str);
  if (isInteger) {
    return { isValid: true, type: "integer" };
  }

  // 检查是否为浮点数（包含科学计数法）
  const isFloat = /^[-+]?(?:\d+\.\d+|\.\d+|\d+\.?[eE][-+]?\d+)$/.test(str);
  if (isFloat) {
    return { isValid: true, type: "float" };
  }

  // 排除特殊值
  if (["infinity", "nan"].includes(str.toLowerCase())) {
    return { isValid: false, type: null };
  }

  return { isValid: false, type: null };
};

// 表格配置项
const columns = reactive<ColumnProps[]>([
  {
    type: "index",
    label: t("device.remoteYm.sequence"),
    width: 60
  },
  {
    prop: "name",
    label: t("device.remoteYm.shortAddress"),
    width: 300,
    search: {
      el: "input",
      tooltip: t("device.remoteYm.inputShortAddress"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "desc",
    label: t("device.remoteYm.description"),
    width: 400,
    search: {
      el: "input",
      tooltip: t("device.remoteYm.inputDescription"),
      props: {
        onKeyup: (e: KeyboardEvent) => {
          if (e.key === "Enter") {
            proTable.value?.search();
          }
        }
      }
    }
  },
  {
    prop: "value",
    label: t("device.remoteYm.value"),
    width: 180,
    sortable: true,
    sortMethod: (a: any, b: any) => {
      // 将值转换为数字进行比较
      const numA = parseFloat(a.value) || 0;
      const numB = parseFloat(b.value) || 0;
      return numA - numB;
    },
    render: scope => {
      const handleChange = async (value: string, row: any) => {
        console.log(row);
        const { isValid } = isInputValid(value);
        if (!isValid) {
          addConsole(t("device.remoteYm.invalidData", { name: row.name, value: value || "" }));
          ElMessageBox.alert(t("device.remoteYm.invalidData", { name: row.name, value: value || "" }), t("device.remoteYm.error"), {
            type: "warning"
          });
          await nextTick(() => {
            row.value = row.lastValid;
          });
          return;
        } else {
          row.lastValid = row.value;
        }
        console.log("Value changed:", value);
      };
      return (
        <div>
          <el-input v-model={scope.row.value} onChange={(value: string) => handleChange(value, scope.row)} />
        </div>
      );
    }
  },
  { prop: "operation", label: t("device.remoteYm.operation"), fixed: "right", width: 330 }
]);
onMounted(() => {
  getData();
});
watch(
  debugIndex.compData,
  newValue => {
    console.log("newValue:", newValue);
    if (newValue) {
      console.log("grpName", newValue);
      proTable.value?.reset();
      getData();
    }
  },
  { deep: true }
);
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 5px;
}
</style>
